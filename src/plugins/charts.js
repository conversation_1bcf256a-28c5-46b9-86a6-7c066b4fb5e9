import ApexCharts from 'apexcharts'

// helper to mount any chart by selector + options
function render<PERSON>hart(selector, opts) {
  const el = document.querySelector(selector)
  if (!el) return
  new ApexCharts(el, opts).render()
}

// ———————— Chart Configuration Imports ————————
// Candlesticks
import candle1Opts from './apexcharts/candlestick-1.js'
import candle2Opts from './apexcharts/candlestick-2.js'
import candle3Opts from './apexcharts/candlestick-3.js'
import candle4Opts from './apexcharts/candlestick-4.js'
import candle5Opts from './apexcharts/candlestick-5.js'

// Column Charts
import col1Opts from './apexcharts/column-chart-1.js'
import col2Opts from './apexcharts/column-chart-2.js'
import col3Opts from './apexcharts/column-chart-3.js'
import col4Opts from './apexcharts/column-chart-4.js'
import col5Opts from './apexcharts/column-chart-5.js'
import col6Opts from './apexcharts/column-chart-6.js'
import col7Opts from './apexcharts/column-chart-7.js'
import col8Opts from './apexcharts/column-chart-8.js'
import col9Opts from './apexcharts/column-chart-9.js'

// Line Charts
import lineChart from './apexcharts/line-chart-1.js'
import lineChartTwoline from './apexcharts/line-chart-twoline.js'

// Donut Charts
import donut from './apexcharts/donut-1.js'

// Small Charts
import smallChart1 from './apexcharts/small-chart-1.js'
import smallChart2 from './apexcharts/small-chart-2.js'
import smallChart3 from './apexcharts/small-chart-3.js'
import smallChart4 from './apexcharts/small-chart-4.js'
import smallChart5 from './apexcharts/small-chart-5.js'
import smallChart6 from './apexcharts/small-chart-6.js'

// ———————— Dynamic Chart Registry ————————
export const chartRegistry = {
  // Candlestick charts
  'candlestick-1': candle1Opts,
  'candlestick-2': candle2Opts,
  'candlestick-3': candle3Opts,
  'candlestick-4': candle4Opts,
  'candlestick-5': candle5Opts,

  // Column charts
  'column-chart-1': col1Opts,
  'column-chart-2': col2Opts,
  'column-chart-3': col3Opts,
  'column-chart-4': col4Opts,
  'column-chart-5': col5Opts,
  'column-chart-6': col6Opts,
  'column-chart-7': col7Opts,
  'column-chart-8': col8Opts,
  'column-chart-9': col9Opts,

  // Line charts
  'line-chart-1': lineChart,
  'line-chart-twoline': lineChartTwoline,

  // Donut charts
  'donut-1': donut,

  // Small charts
  'small-chart-1': smallChart1,
  'small-chart-2': smallChart2,
  'small-chart-3': smallChart3,
  'small-chart-4': smallChart4,
  'small-chart-5': smallChart5,
  'small-chart-6': smallChart6,
}

// Function to get chart configuration by type
export function getChartConfig(chartType) {
  return chartRegistry[chartType] || null
}

// Function to get all available chart types
export function getAvailableChartTypes() {
  return Object.keys(chartRegistry)
}

export function initCandlestick1() {
  renderChart('#candlestick-1', candle1Opts)
}
export function initCandlestick2() {
  renderChart('#candlestick-2', candle2Opts)
}
export function initCandlestick3() {
  renderChart('#candlestick-3', candle3Opts)
}
export function initCandlestick4() {
  renderChart('#candlestick-4', candle4Opts)
}
export function initCandlestick5() {
  renderChart('#candlestick-5', candle5Opts)
}

// ———————— Column Charts ————————
// each of these files should default-export an `opts` object
import col1Opts from './apexcharts/column-chart-1.js'
import col2Opts from './apexcharts/column-chart-2.js'
import col3Opts from './apexcharts/column-chart-3.js'
import col4Opts from './apexcharts/column-chart-4.js'

export function initColumnChart1() {
  renderChart('#column-chart-1', col1Opts)
}
export function initColumnChart2() {
  renderChart('#column-chart-2', col2Opts)
}
export function initColumnChart3() {
  renderChart('#column-chart-3', col3Opts)
}
export function initColumnChart4() {
  renderChart('#column-chart-4', col4Opts)
}

import donut from './apexcharts/donut-1.js'

export function initDonutChart() {
  renderChart('#donut-1', donut)
}

import lineChart from './apexcharts/line-chart-1.js'
import lineChartTwoline from './apexcharts/line-chart-twoline.js'

export function initLineChart() {
  renderChart('#line-chart-1', donut)
}
export function initLineChartTwoline() {
  renderChart('#line-chart-twoline', donut)
}

import smallChart1 from './apexcharts/small-chart-1.js'
import smallChart2 from './apexcharts/small-chart-2.js'
import smallChart3 from './apexcharts/small-chart-3.js'
import smallChart4 from './apexcharts/small-chart-4.js'
import smallChart5 from './apexcharts/small-chart-5.js'
import smallChart6 from './apexcharts/small-chart-6.js'

export function initSmallChart1() {
  renderChart('#small-chart-1', smallChart1)
}
export function initSmallChart2() {
  renderChart('#small-chart-2', smallChart2)
}
export function initSmallChart3() {
  renderChart('#small-chart-3', smallChart3)
}
export function initSmallChart4() {
  renderChart('#small-chart-4', smallChart4)
}
export function initSmallChart5() {
  renderChart('#small-chart-5', smallChart5)
}
export function initSmallChart6() {
  renderChart('#small-chart-6', smallChart6)
}

// ———————— Bundle initializer ————————
export function initCharts() {
  // candlesticks
  initCandlestick1()
  initCandlestick2()
  initCandlestick3()
  initCandlestick4()
  initCandlestick5()

  // column charts
  initColumnChart1()
  initColumnChart2()
  initColumnChart3()
  initColumnChart4()

  // donut
  initDonutChart()

  // line charts
  initLineChart()
  initLineChartTwoline()

  // small charts
  initSmallChart1()
  initSmallChart2()
  initSmallChart3()
  initSmallChart4()
  initSmallChart5()
  initSmallChart6()
}
