import ApexCharts from 'apexcharts'

// helper to mount any chart by selector + options
function render<PERSON>hart(selector, opts) {
  const el = document.querySelector(selector)
  if (!el) return
  new ApexCharts(el, opts).render()
}

// ———————— Chart Configuration Imports ————————
// Candlesticks
import candle1Opts from './apexcharts/candlestick-1.js'
import candle2Opts from './apexcharts/candlestick-2.js'
import candle3Opts from './apexcharts/candlestick-3.js'
import candle4Opts from './apexcharts/candlestick-4.js'
import candle5Opts from './apexcharts/candlestick-5.js'

// Column Charts (only ES6 export format)
import col1Opts from './apexcharts/column-chart-1.js'
import col2Opts from './apexcharts/column-chart-2.js'
import col3Opts from './apexcharts/column-chart-3.js'
import col4Opts from './apexcharts/column-chart-4.js'

// Line Charts
import lineChart from './apexcharts/line-chart-1.js'
import lineChartTwoline from './apexcharts/line-chart-twoline.js'
import lineChartTwolineImproved from './apexcharts/line-chart-twoline-improved.js'

// Donut Charts
import donut from './apexcharts/donut-1.js'

// Small Charts
import smallChart1 from './apexcharts/small-chart-1.js'
import smallChart2 from './apexcharts/small-chart-2.js'
import smallChart3 from './apexcharts/small-chart-3.js'
import smallChart4 from './apexcharts/small-chart-4.js'
import smallChart5 from './apexcharts/small-chart-5.js'
import smallChart6 from './apexcharts/small-chart-6.js'

// ———————— Dynamic Chart Registry ————————
export const chartRegistry = {
  // Candlestick charts
  'candlestick-1': candle1Opts,
  'candlestick-2': candle2Opts,
  'candlestick-3': candle3Opts,
  'candlestick-4': candle4Opts,
  'candlestick-5': candle5Opts,

  // Column charts (only ES6 export format)
  'column-chart-1': col1Opts,
  'column-chart-2': col2Opts,
  'column-chart-3': col3Opts,
  'column-chart-4': col4Opts,

  // Line charts
  'line-chart-1': lineChart,
  'line-chart-twoline': lineChartTwoline,

  // Donut charts
  'donut-1': donut,

  // Small charts
  'small-chart-1': smallChart1,
  'small-chart-2': smallChart2,
  'small-chart-3': smallChart3,
  'small-chart-4': smallChart4,
  'small-chart-5': smallChart5,
  'small-chart-6': smallChart6,
}

// Function to get chart configuration by type
export function getChartConfig(chartType) {
  return chartRegistry[chartType] || null
}

// Function to get all available chart types
export function getAvailableChartTypes() {
  return Object.keys(chartRegistry)
}

// ———————— Legacy Individual Chart Initializers ————————
export function initCandlestick1() {
  renderChart('#candlestick-1', candle1Opts)
}
export function initCandlestick2() {
  renderChart('#candlestick-2', candle2Opts)
}
export function initCandlestick3() {
  renderChart('#candlestick-3', candle3Opts)
}
export function initCandlestick4() {
  renderChart('#candlestick-4', candle4Opts)
}
export function initCandlestick5() {
  renderChart('#candlestick-5', candle5Opts)
}

export function initColumnChart1() {
  renderChart('#column-chart-1', col1Opts)
}
export function initColumnChart2() {
  renderChart('#column-chart-2', col2Opts)
}
export function initColumnChart3() {
  renderChart('#column-chart-3', col3Opts)
}
export function initColumnChart4() {
  renderChart('#column-chart-4', col4Opts)
}

export function initDonutChart() {
  renderChart('#donut-1', donut)
}

export function initLineChart() {
  renderChart('#line-chart-1', lineChart)
}
export function initLineChartTwoline() {
  renderChart('#line-chart-twoline', lineChartTwoline)
}

export function initSmallChart1() {
  renderChart('#small-chart-1', smallChart1)
}
export function initSmallChart2() {
  renderChart('#small-chart-2', smallChart2)
}
export function initSmallChart3() {
  renderChart('#small-chart-3', smallChart3)
}
export function initSmallChart4() {
  renderChart('#small-chart-4', smallChart4)
}
export function initSmallChart5() {
  renderChart('#small-chart-5', smallChart5)
}
export function initSmallChart6() {
  renderChart('#small-chart-6', smallChart6)
}

// ———————— Dynamic Chart Initializer ————————
export function initChart(chartType, selector) {
  const config = getChartConfig(chartType)
  if (config) {
    renderChart(selector || `#${chartType}`, config)
  } else {
    console.warn(`Chart type "${chartType}" not found in registry`)
  }
}

// ———————— Bundle initializer ————————
export function initCharts() {
  // candlesticks
  initCandlestick1()
  initCandlestick2()
  initCandlestick3()
  initCandlestick4()
  initCandlestick5()

  // column charts
  initColumnChart1()
  initColumnChart2()
  initColumnChart3()
  initColumnChart4()

  // donut
  initDonutChart()

  // line charts
  initLineChart()
  initLineChartTwoline()

  // small charts
  initSmallChart1()
  initSmallChart2()
  initSmallChart3()
  initSmallChart4()
  initSmallChart5()
  initSmallChart6()
}

// ———————— Dynamic Bundle initializer ————————
export function initAllCharts() {
  // Initialize all charts dynamically using the registry
  Object.keys(chartRegistry).forEach(chartType => {
    initChart(chartType)
  })
}
