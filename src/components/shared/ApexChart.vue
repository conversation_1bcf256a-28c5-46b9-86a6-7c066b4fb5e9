<script setup>
import { ref, onMounted, watch, onBeforeUnmount, computed } from 'vue'
import ApexCharts from 'apexcharts'
import { getChartConfig } from '@/plugins/charts.js'

const props = defineProps({
  chartId: { type: String, required: true },
  chartType: { type: String, default: null },
  series: { type: Array, default: () => [] },
  options: { type: Object, default: () => ({}) },
})

// hold the chart instance
const container = ref(null)
let chart = null

// Helper function for deep cloning objects while preserving Date objects
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (Array.isArray(obj)) return obj.map(item => deepClone(item))

  const cloned = {}
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  return cloned
}

// Helper function for deep merging objects
function deepMerge(target, source) {
  const result = { ...target }

  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key]) && !(source[key] instanceof Date)) {
      result[key] = deepMerge(target[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  }

  return result
}

// Computed property to get the final chart configuration
const chartConfig = computed(() => {
  let baseConfig = {}

  // If chartType is provided, load configuration from registry
  if (props.chartType) {
    const registryConfig = getChartConfig(props.chartType)
    if (registryConfig) {
      // Deep clone the registry config to avoid mutations
      baseConfig = JSON.parse(JSON.stringify(registryConfig))
    } else {
      console.warn(`Chart type "${props.chartType}" not found in registry`)
    }
  }

  // Deep merge with user-provided options
  const finalConfig = deepMerge(baseConfig, props.options)

  // Handle series - user-provided series takes precedence
  if (props.series.length > 0) {
    finalConfig.series = props.series
  }

  return finalConfig
})

function mountChart() {
  chart = new ApexCharts(container.value, chartConfig.value)
  chart.render()
}

onMounted(mountChart)

// Watch for changes in chart configuration
watch(
  chartConfig,
  (newConfig) => {
    if (chart) {
      // Destroy and recreate chart when configuration changes significantly
      chart.destroy()
      chart = new ApexCharts(container.value, newConfig)
      chart.render()
    }
  },
  { deep: true }
)

// if `series` updates, push them to chart
watch(
  () => props.series,
  (newSeries) => {
    if (chart && newSeries.length > 0) {
      chart.updateSeries(newSeries, /* animate = */ true)
    }
  },
  { deep: true },
)

// if other options change (axes, colors, etc.), updateOptions()
watch(
  () => props.options,
  (newOpts) => {
    if (chart && Object.keys(newOpts).length > 0) {
      // For significant changes, recreate the chart
      chart.destroy()
      chart = new ApexCharts(container.value, chartConfig.value)
      chart.render()
    }
  },
  { deep: true },
)

// Watch for chartType changes
watch(
  () => props.chartType,
  () => {
    if (chart) {
      // Recreate chart when type changes
      chart.destroy()
      chart = new ApexCharts(container.value, chartConfig.value)
      chart.render()
    }
  }
)

onBeforeUnmount(() => {
  if (chart) chart.destroy()
})
</script>

<template>
  <div :id="chartId" ref="container"></div>
</template>

<style scoped></style>
