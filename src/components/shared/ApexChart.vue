<script setup>
import { ref, onMounted, watch, onBeforeUnmount, computed } from 'vue'
import ApexCharts from 'apexcharts'
import { getChartConfig } from '@/plugins/charts.js'

const props = defineProps({
  chartId: { type: String, required: true },
  chartType: { type: String, default: null },
  series: { type: Array, default: () => [] },
  options: { type: Object, default: () => ({}) },
})

// hold the chart instance
const container = ref(null)
let chart = null

// Computed property to get the final chart configuration
const chartConfig = computed(() => {
  let baseConfig = {}

  // If chartType is provided, load configuration from registry
  if (props.chartType) {
    const registryConfig = getChartConfig(props.chartType)
    if (registryConfig) {
      baseConfig = { ...registryConfig }
    } else {
      console.warn(`Chart type "${props.chartType}" not found in registry`)
    }
  }

  // Merge with user-provided options and series
  const finalConfig = {
    ...baseConfig,
    ...props.options,
  }

  // Handle series - user-provided series takes precedence
  if (props.series.length > 0) {
    finalConfig.series = props.series
  }

  return finalConfig
})

function mountChart() {
  chart = new ApexCharts(container.value, chartConfig.value)
  chart.render()
}

onMounted(mountChart)

// if `series` updates, push them to chart
watch(
  () => props.series,
  (newSeries) => {
    if (chart) chart.updateSeries(newSeries, /* animate = */ true)
  },
  { deep: true },
)

// if other options change (axes, colors, etc.), updateOptions()
watch(
  () => props.options,
  (newOpts) => {
    if (chart) chart.updateOptions(newOpts, /* redraw = */ true)
  },
  { deep: true },
)

onBeforeUnmount(() => {
  if (chart) chart.destroy()
})
</script>

<template>
  <div :id="chartId" ref="container"></div>
</template>

<style scoped></style>
