<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import ApexCharts from 'apexcharts'

const props = defineProps({
  chartId: { type: String, required: true },
  series: { type: Array, default: () => [] },
  options: { type: Object, default: () => ({}) },
})

// hold the chart instance
const container = ref(null)
let chart = null

function mountChart() {
  // merge user‐passed `series` with their other options
  const opts = {
    series: props.series,
    ...props.options,
  }
  chart = new ApexCharts(container.value, opts)
  chart.render()
}

onMounted(mountChart)

// if `series` updates, push them to chart
watch(
  () => props.series,
  (newSeries) => {
    if (chart) chart.updateSeries(newSeries, /* animate = */ true)
  },
  { deep: true },
)

// if other options change (axes, colors, etc.), updateOptions()
watch(
  () => props.options,
  (newOpts) => {
    if (chart) chart.updateOptions(newOpts, /* redraw = */ true)
  },
  { deep: true },
)

onBeforeUnmount(() => {
  if (chart) chart.destroy()
})
</script>

<template>
  <div :id="chartId" ref="container"></div>
</template>

<style scoped></style>
