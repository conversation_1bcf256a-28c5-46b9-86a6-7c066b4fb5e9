<template>
  <div class="final-test">
    <h3>Final Chart Test - Should Work Now!</h3>
    
    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>✅ Candlestick Chart (Dynamic)</h4>
          <ApexChart
            chart-id="final-candlestick"
            chart-type="candlestick-1"
            :options="{ 
              chart: { height: 350 },
              title: { text: 'Dynamic Candlestick Chart' }
            }"
          />
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="test-item">
          <h4>✅ Column Chart (Dynamic)</h4>
          <ApexChart
            chart-id="final-column"
            chart-type="column-chart-1"
            :options="{ 
              chart: { height: 350 },
              title: { text: 'Dynamic Column Chart' },
              xaxis: { labels: { show: true } },
              yaxis: { show: true }
            }"
          />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-4">
        <div class="test-item">
          <h4>✅ Line Chart (Dynamic)</h4>
          <ApexChart
            chart-id="final-line"
            chart-type="line-chart-twoline"
            :options="{ 
              chart: { height: 250 },
              title: { text: 'Dynamic Line Chart' }
            }"
          />
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="test-item">
          <h4>✅ Donut Chart (Dynamic)</h4>
          <ApexChart
            chart-id="final-donut"
            chart-type="donut-1"
            :options="{ 
              chart: { height: 250 },
              title: { text: 'Dynamic Donut Chart' }
            }"
          />
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="test-item">
          <h4>✅ Small Chart (Dynamic)</h4>
          <ApexChart
            chart-id="final-small"
            chart-type="small-chart-1"
            :options="{ 
              chart: { height: 100 }
            }"
          />
        </div>
      </div>
    </div>
    
    <div class="success-message">
      <h4>🎉 Dynamic Chart System Working!</h4>
      <p>All charts above are rendered using the dynamic chart system with chart-type props.</p>
      <ul>
        <li><strong>Candlestick:</strong> Date objects preserved correctly</li>
        <li><strong>Column:</strong> Stacked bar chart with proper configuration</li>
        <li><strong>Line:</strong> Multi-series line chart</li>
        <li><strong>Donut:</strong> Pie chart with custom colors</li>
        <li><strong>Small:</strong> Sparkline area chart</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import ApexChart from '@/components/shared/ApexChart.vue'
</script>

<style scoped>
.final-test {
  padding: 20px;
}

.test-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.test-item h4 {
  margin-top: 0;
  color: #28a745;
}

.success-message {
  margin-top: 30px;
  padding: 20px;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  color: #155724;
}

.success-message h4 {
  color: #155724;
  margin-top: 0;
}

.success-message ul {
  margin-bottom: 0;
}
</style>
