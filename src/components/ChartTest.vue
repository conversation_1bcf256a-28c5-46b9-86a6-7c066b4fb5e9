<template>
  <div class="chart-test">
    <h3>Dynamic Chart System Test</h3>
    
    <!-- Test 1: Basic dynamic chart -->
    <div class="test-section">
      <h4>Test 1: Line Chart Two-line</h4>
      <ApexChart
        chart-id="test-line-chart"
        chart-type="line-chart-twoline"
        :options="{ chart: { height: 200 } }"
      />
    </div>
    
    <!-- Test 2: Candlestick chart -->
    <div class="test-section">
      <h4>Test 2: Candlestick Chart</h4>
      <ApexChart
        chart-id="test-candlestick"
        chart-type="candlestick-1"
        :options="{ chart: { height: 200 } }"
      />
    </div>
    
    <!-- Test 3: Column chart -->
    <div class="test-section">
      <h4>Test 3: Column Chart</h4>
      <ApexChart
        chart-id="test-column"
        chart-type="column-chart-1"
        :options="{ chart: { height: 200 } }"
      />
    </div>
    
    <!-- Test 4: Small chart -->
    <div class="test-section">
      <h4>Test 4: Small Chart</h4>
      <ApexChart
        chart-id="test-small"
        chart-type="small-chart-1"
      />
    </div>
    
    <!-- Test 5: Registry info -->
    <div class="test-section">
      <h4>Test 5: Available Chart Types</h4>
      <p>Total charts available: {{ availableChartTypes.length }}</p>
      <ul>
        <li v-for="type in availableChartTypes" :key="type">{{ type }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import ApexChart from '@/components/shared/ApexChart.vue'
import { useCharts } from '@/composables/useCharts.js'

const { availableChartTypes } = useCharts()
</script>

<style scoped>
.chart-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h4 {
  margin-top: 0;
  color: #333;
}

ul {
  max-height: 200px;
  overflow-y: auto;
  columns: 2;
}
</style>
